Last updated by | <PERSON> | Mar 12, 2025 at 10:48 AM CDT
RPM API
Authorization
Authorization to the RPM API is achieved via a GET request to the URL path
/api/v1/associate/{account}/{deviceId} where {account} is the 4-digit account number of the organization
and {deviceId} is the unique ID of the device, such as the IMEI.
Example: https://rpm.510.test.safensound.io/api/v1/associate/1234/************
The response consists of a JSON object containing either login credentials for the organization or an error
message. The JSON structure is as follows:
{
"name": "FHIR",
"version": 1,
{
"authToken": string, // BASIC Auth token for JWT request
"loginURL": string, // Base URL to HTTP POST to get JWT token
"baseUri": string, // URI to append to login URL for POST request
"accountNumber": string, // 4-digit SNS account number
"errorMessage": string
}
}

If an error message is present, authentication should stop.
Example Success:
{
"name": "FHIR",
"version": "1",
"payload": {
"loginUrl": "https://rpm.510.test.safensound.io",
"baseUri": "/oauth/token?accountNumber=2000&username=hubapi&Origin=EMR&Token=zZ8*hT4%7EeO6%5E
"accountNumber": "2000",
"authToken": "NGQyMGY0MzgtNTMyOS0xMWVmLTkwYjAtMDI0MmFjMTEwMDAyOjRkMjBmNDQzLTUzMjktMTFlZi05\nM
}
}

Example Generic Error:
{
"name": "FHIR",
"version": "1",
"payload": {
"errorMessage" : "Failed to associate device ************ to organization 2000"
}
}

Example Error When Device Not-Registered
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 1/29
Incorrect Authorization header value
User is disabled/inactive/locked
No route to destination
FHIR Operations
{
"name": "FHIR",
"version": "1",
"payload": {
"errorMessage" : "Invalid device UUID"
}
}

Now, retrieve the JWT token from the provided endpoint:
curl -XPOST -H'Authorization: Basic
YjUyNDQ4NjJhZDEyMGUxMmJhYzkzZDI0MjRhOTMwZDU6OTNlMDA2NTU3MGUzOWI2MzMyZGYxZWY5ODNhMjQ0MTQ='
"https://rpm.510.test.safensound.io/oauth/token?
accountNumber=****&username=******&Origin=***&Token=***************************"
This command returns a JSON object containing the JWT access_token and the JWT refresh_token. The
structure is as follows:
{
"role": "HUB",
"liveViewOnly": true,
"mustChangePassword": "false",
"themeMode": "LIGHT",
"token_type": "Bearer",
"accountNumber": "****",
"userName": "Hub Api User",
"userId": 20950,
"access_token": "eyJhY2NvdW50TnVtYmVyIjoiMjAwMCIsInVzZXJJZCI6MjA5NTAsImFsZyI6IlJTMjU2Iiwia2lk
"organizationId": 768,
"refresh_token": "eyJhY2NvdW50TnVtYmVyIjoiMjAwMCIsInVzZXJJZCI6MjA5NTAsImFsZyI6IlJTMjU2Iiwia2l
"scope": "HUB HUB_API",
"pinLength": 6,
"expires_in": 86398,
"passcode": "null"
}

Upon success, all subsequent operations will be performed via FHIR over a WebSocket. Possible reasons for
error would include the following:
For the first two scenarios above, the API call will return an HTTP 401 Unauthorized error code. For the second,
contact the local IT network administration for assistance troubleshooting connectivity to SafeNSound.
If someone attempts to use a JWT from one registered device to try and connect to the RPM server with an
unregistered device ID, the RPM server will detect this condition and close the WebSocket session with a close
status of NOT_ACCEPTABLE (Value = 1003)
WebSocket Endpoint
/api/v1/fhir/{account}/{deviceId}/store
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 2/29
FHIR Flow Diagram
Observation (Waveforms)
After successful authentication, use the access_token value to connect to the FHIR WebSocket by placing the
access_token in the Authorization: Bearer HTTP header value when connecting to the WebSocket.
Example:
websocat -H='Authorization: Bearer
eyJhY2NvdW50TnVtYmVyIjoiMjAwMCIsInVzZXJJZCI6MjA5NTAsImFsZyI6IlJTMjU2Iiwia2lkIjoiMzQ3OWIyZjAtYTYyOC00Zj
dmLThlZDAtMGUzYjQzZGFmZjA5In0.eyJzdWIiOiJodWJhcGkiLCJhdWQiOiJib3h2aWV3LWFwaWdhdGV3YXkiLCJuYmYiOjE2NzQx
MjEzNjAsInNjb3BlIjpbIkhVQiIsIkhVQl9BUEkiXSwiaXNzIjoiaHR0cDpcL1wvbG9jYWxob3N0OjcwMDAiLCJleHAiOjE2NzQyMD
c3NjAsImlhdCI6MTY3NDEyMTM2MH0.XjQ-oQ8RidXxd7KS8mW_FTtGz8aoBsrlayhGxkIo0TAztSx83s1oIT3LYRqUm1k-
VKWEx07Lcth_kVGNAP8bSeHPQ-u5a5rtsfVuOKkUHuKfVV74MlR8B2Sk3fcrVNm68DM5Ut_2XV--
BdZRs4gk0yKX2e5qAhjzvHhXmbXvsZu4OY2nTW_s8BtZ2Oo0UxdnJicqBX9wO2077TqDr0pz6sPd1Gg0_xJ951OO3WntdaJfscqM6c
jq-E_jJBaRkWiJyyyTT670VK19ekSdSfW16u3TGz01d_Z1L8U8xVE93eqCAAxqxF4Pv8C-dx0f6XeHVSFCqFpm0aI5XaqV6QeItQ'
wss://rpm.510.test.safensound.io/api/v1/fhir/{account}/{deviceId}/store
where the value of {account} is the 4-digit account number of the target organization.
Once connected, messages may begin to flow bi-directionally.
The observation resource is a record of an observation of an event during a patient encounter.
FHIR Resource: Observation https://www.hl7.org/fhir/observation.html
The following attributes are supported:
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 3/29
Name Comment
id Value of the identifier (a UUID). Required for message
acknowledgment.
subject.reference Reference to a patient currently admitted into the
SafeNSound system.
effectiveDateTime Beginning timestamp of waveform data
device.reference Reference to origin device.
component[].code.coding.system Coding system OID. urn:oid:2.16.840.1.113883.6.24
component[].code.coding.code Integer value of waveform OID. Example: 131330 = ECG
Lead II.
component[].code.coding.display String value of waveform OID. Example 131330 =
MDC_ECG_ELEC_POTL_II
component[].valueSampledData.origin.value Zero value
component[].valueSampledData.period Number of milliseconds between samples
component[].valueSampledData.lowerLimit Lower limit of detection
component[].valueSampledData.upperLimit Upper limit of detection
component[].valueSampledData.dimensions Number of sample points at each time point
component[].valueSampledData.data
Decimal values delineated with spaces with each value
indicating a sample. Make each value a CSV tuple to add
flags to the sample.
Note: the origin value is expected to be equidistant from the upper value and lower value. Spacelabs scaling
code uses the difference between the upper limit value and the origin to produce a scaling factor thus we
expect the values to be consistent and equidistant.
MDC Waveform Codes
MDC_ECG_LEAD_A(131143), // ECG Lead A
MDC_PULS_OXIM_PLETH(150452), // SpO2
MDC_RESP_RATE(151562) // Respiration Rate

Example Request Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 4/29
{
"resourceType" : "Observation",
"id" : "15774c77-7a81-11ed-897b-0e0713e7f860",
"status" : "final",
"category" : [{
"coding" : [{
"system" : "http://terminology.hl7.org/CodeSystem/observation-category",
"code" : "procedure",
"display" : "Procedure"
}]
}],
"subject" : {
"reference" : "Patient/f001",
"display" : "P. van de Heuvel"
},
"effectiveDateTime" : "2015-02-19T09:30:35+01:00",
"performer" : [{
"reference" : "Practitioner/f005",
"display" : "A. Langeveld"
}],
"device" : {
"reference": "Device/15774c77-7a81-11ed-897b-0e0713e7f869",
"display" : "12 lead EKG Device Metric"
},
"component" : [
{
"code" : {
"coding" : [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "131143",
"display" : "MDC_ECG_LEAD_A"
}]
},
"valueSampledData" : {
"origin" : {
"value" : 2048
},
"period" : 10,
"factor" : 1.612,
"lowerLimit" : -3300,
"upperLimit" : 3300,
"dimensions" : 1,
"data" : "2041 2043 2037 2047 2060 2062 2051 2023 2014 2027 2034 2033 2040 2047 2047 2053 205
}
}
]
}

Example Success Response Message
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "information",
"details": {
"text": "Waveform operation queued successfully"
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 5/29
Example Error Response Messages
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Unhandled event `{json data}'"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot find device by UUID `1234'"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate patient"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "All waveform entries were invalid. Check the coding."
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 6/29
Observation (Numerics)
Name Comment
id Value of the identifier (a UUID). Required for message
acknowledgment.
subject.reference Reference to a patient currently admitted into the SafeNSound
system.
effectiveDateTime Beginning timestamp of vital data
device.reference Reference to origin device.
component[].code[].coding.system Coding system OID. urn:oid:2.16.840.1.113883.6.24 or
http://loinc.org
component[].code[].coding.code MDC or LOINC vital code from the below lists, depending on coding
system used
component[].valueQuantity.value Double value of vital.
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "No operation to consume"
}
}]
}

The observation resource is a record of an observation of an event during a patient encounter.
FHIR Resource: Observation https://www.hl7.org/fhir/observation.html
The following attributes are supported:
MDC Vital Codes
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 7/29
MDC_ECG_HEART_RATE("147842"), /* Heart Rate */
MDC_RESP_RATE("151554"), /* Resp Rate */
MDC_SAT_O2("150316"), /* Oxygen Saturation */
MDC_TEMP_BODY("150364"), /* Body Temp */
MDC_TEMP_SURF("188444"), /* Surface Temp */
MDC_TEMP_SKIN("150388"), /* Skin Temp */
MDC_PRESS_BLD_SYS("150017"), /* Systolic Blood Pressure */
MDC_PRESS_BLD_DIA("150018"), /* Diastolic Blood Pressure */
MDC_PRESS_BLD_MEAN("150019"), /* Mean Blood Pressure */
MDC_AI_EVT_FALL_DETECTED("264918"), /* Fall Count */
MDC_AI_TYPE_BASE_COORD_ANGLE("8519760") /* Body Position - Angle (in degrees) */,
MDC_AI_TYPE_BASE_LOCATION("8519764"), /* Body Position - Roll Angle */
MDC_DEV_SUB_SPEC_PROFILE_STEP_COUNTER("528484"), /* Step Count */
MDC_BODY_POSITION("126977"), /* Body Position */
MDC_PULS_OXIM_PULS_RATE("149530"), /* Pulse Rate (SpO2) */
MDC_BLD_PERF_INDEX("150488"), /* Perfusion Index */
MDC_PULS_RATE_NON_INV("149546") /* Pulse Rate (NIBP) */

LOINC Vital Codes
LONIC_HR("8867-4"),
LOINC_RR("9279-1"),
LOINC_SP02("2708-6"),
LOINC_PULSE_RATE("8889-8"),
LOINC_PERFUSION_INDEX("73798-1"),
LOINC_BODY_TEMPERATURE("8310-5"),
LOINC_BODY_SURFACE_TEMPERATURE("61008-9"),
LOINC_BODY_TEMPERATURE_HAND("98657-0"),
LOINC_BP_PULSE_RATE("76477-9"),
LOINC_SYSTOLIC("8480-6"),
LOINC_DIASTOLIC("8462-4"),
LOINC_MEAN("8478-0"),
LOINC_BODY_POSITION("8361-8"),
LOINC_STEP_COUNT_UNSPEC_TIME("55423-8")

Example Request Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 8/29
{
"resourceType": "Observation",
"id": "15774c77-7a81-11ed-897b-0e0713e7f861",
"status": "final",
"category": [
{
"coding": [
{
"system": "http://terminology.hl7.org/CodeSystem/observation-category",
"code": "vital-signs",
"display": "Vital Signs"
}
]
}
],
"subject": {
"reference": "Patient/example"
},
"effectiveDateTime": "2012-09-17T16:20:45+00:00",
"performer": [
{
"reference": "Practitioner/example"
}
],
"component": [
{
"code": {
"coding": [
{
"system": "urn:oid:2.16.840.1.113883.6.24",
"code": "150017",
"display": "MDC_PRESS_BLD_SYS"
}
]
},
"valueQuantity": {
"value": 107,
"unit": "mmHg",
"system": "http://unitsofmeasure.org",
"code": "mm[Hg]"
}
},
{
"code": {
"coding": [
{
"system": "urn:oid:2.16.840.1.113883.6.24",
"code": "150018",
"display": "MDC_PRESS_BLD_DIA"
}
]
},
"valueQuantity": {
"value": 60,
"unit": "mmHg",
"system": "http://unitsofmeasure.org",
"code": "mm[Hg]"
}
},
{
"code": {
"coding": [
{
"system": "urn:oid:2.16.840.1.113883.6.24",

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 9/29
"code": "147842",
"display": "MDC_ECG_HEART_RATE"
}
]
},
"valueQuantity": {
"value": 44,
"unit": "beats/minute",
"system": "http://unitsofmeasure.org",
"code": "/min"
}
},
{
"code": {
"coding": [
{
"system": "urn:oid:2.16.840.1.113883.6.24",
"code": "528484",
"display": "MDC_DEV_SUB_SPEC_PROFILE_STEP_COUNTER"
}
],
"text": "Step Count"
},
"valueQuantity": {
"value": 1440
}
},
{
"code": {
"coding": [
{
"system": "urn:oid:2.16.840.1.113883.6.24",
"code": "264918",
"display": "MDC_AI_EVT_FALL_DETECTED"
}
],
"text": "Fall Count"
},
"valueQuantity": {
"value": 0
}
},
{
"code": {
"coding": [
{
"system": "urn:oid:2.16.840.1.113883.6.24",
"code": "8519760",
"display": "MDC_AI_TYPE_BASE_COORD_ANGLE"
}
]
},
"valueQuantity": {
"value": 17,
"unit": "degree",
"system": "http://unitsofmeasure.org",
"code": "deg"
}
},
{
"code": {
"coding": [
{
"code": "9279-1",
"display": "Respiratory rate",
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 10/29
"system": "http://loinc.org"
}
],
"text": "Respiratory rate"
},
"valueQuantity": {
"code": "/min",
"system": "http://unitsofmeasure.org",
"unit": "breaths/minute",
"value": "19.0"
}
},
{
"code": {
"coding": [
{
"code": "8310-5",
"display": "Temperature",
"system": "http://loinc.org"
}
],
"text": "Temperature"
},
"valueQuantity": {
"code": "/min",
"system": "http://unitsofmeasure.org",
"unit": "Cel",
"value": "32.0"
}
},
{
"code": {
"coding": [
{
"code": "126977",
"display": "MDC_BODY_POSITION",
"system": "urn:oid:2.16.840.1.113883.6.24"
}
],
"text": "Temperature"
},
"valueQuantity": {
"value": "3"
/* Value between 1-5 to indicate positioning
case "1" -> "Upright";
case "2" -> "Supine";
case "3" -> "Prone";
case "4" -> "Right";
case "5" -> "Left";
*/
}
}
]
}
Example Success Response Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 11/29
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f861",
"issue": [{
"severity": "information",
"details": {
"text": "Create HR vital queued successfully"
}
}]
}

Example Error Response Messages
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Unhandled event `{json data}'"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate device"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate patient"
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 12/29
Observation (Limit Alarms)
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Failed to create vital HR"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "All vitals entries were invalid. Check the coding."
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "No operation to consume"
}
}]
}

NOTE: Any blood pressure message must include both systolic and diastolic values in a single message to be
processed as a vital sign.
The observation resource is a record of an observation of an event during a patient encounter.
FHIR Resource: Observation https://www.hl7.org/fhir/observation.html
The following attributes are supported:
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 13/29
Name Comment
id Value of the identifier (a UUID). Required for message
acknowledgment.
meta.tag[].display
Parameter name, if present, can be "eventTime", "extreme",
or "acknowledged". Event time is a timestamp used for
interim alarm state reporting. Extreme is used to identify
alarm values that have exceeded both the high level and an
"extreme" level. The acknowledged parameter marks the
alarm as acknowledged by the caregiver and should only
be set when the action is performed. The time to
acknowledge will be calculated from this event.
meta.tag[].code Parameter value
subject.reference Reference to a patient currently admitted into the
SafeNSound system.
effectivePeriod.start Beginning timestamp of alarm data. Required.
effectivePeriod.end Ending timestamp of alarm data. Optional (until alarm
ends).
device.reference Reference to origin device.
component[].code[].coding.system Static value of http://loinc.org
component[].code[].coding.code LOINC vital code from https://build.fhir.org/valueset-
observation-vitalsignresult.html
component[].valueQuantity.value Double value of vital.
component[].interpretation[].coding[].code
Valid values are L, N, H. Can review changing to
accommodate https://terminology.hl7.org/5.1.0/ValueSet-
v3-ObservationInterpretation.html
component[].interpretation[].coding[].display Valid values are Low, Normal, High
component[].valueQuantity.value String value of numeric observation
component[].referenceRange.high.value High setting violated by alarm value
component[].referenceRange.low.value Low setting violated by alarm value
Example Start Alarm Request Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 14/29
{
"resourceType": "Observation",
"id": "206",
"status": "final",
"meta": {
"profile": ["http://hl7.org/fhir/StructureDefinition/vitalsigns"],
"tag": [{
"display": "extreme",
"code": "true"
},
{
"display": "acknowledged",
"code": "false"
}
]
},
"device" : {
"reference": "Device/351877479666121",
"display" : "HUB device"
},
"category": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/observation-category",
"code": "vital-signs",
"display": "Vital Signs"
}]
}],
"code": {
"coding": [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "147842",
"display" : "MDC_ECG_HEART_RATE"
}],
"text": "Heart rate"
},
"subject": {
"reference": "Patient/112215555"
},
"effectivePeriod": {
"start": "2012-09-17T13:03:51+00:00"
},
"performer": [{
"reference": "Practitioner/f005"
}],
"interpretation": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
"code": "L",
"display": "low"
}],
"text": "Below low normal"
}],
"component": [{
"code": {
"coding": [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "147842",
"display" : "MDC_ECG_HEART_RATE"
}]
},
"valueQuantity": {
"value": 45,
"unit": "beats/minute",
"system": "http://unitsofmeasure.org",

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 15/29
"code": "/min"
},
"interpretation": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
"code": "L",
"display": "low"
}],
"text": "Below low normal"
}],
"referenceRange": [{
"high": {
"value": 150,
"unit": "beats/minute",
"code": "/min"
},
"low": {
"value": 50,
"unit": "beats/minute",
"code": "/min"
}
}]
}]
}
Example Interim Alarm State Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 16/29
{
"resourceType": "Observation",
"id": "206",
"status": "final",
"meta": {
"profile": ["http://hl7.org/fhir/StructureDefinition/vitalsigns"],
"tag": [
{
"display": "extreme",
"code": "true"
},
{
"display": "acknowledged",
"code": "false"
},
{
"display": "eventTime",
"code": "2012-09-17T13:03:53+00:00"
}
]
},
"device" : {
"reference": "Device/351877479666121",
"display" : "HUB device"
},
"category": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/observation-category",
"code": "vital-signs",
"display": "Vital Signs"
}]
}],
"code": {
"coding": [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "147842",
"display" : "MDC_ECG_HEART_RATE"
}],
"text": "Heart rate"
},
"subject": {
"reference": "Patient/112215555"
},
"effectivePeriod": {
"start": "2012-09-17T13:03:51+00:00"
},
"performer": [{
"reference": "Practitioner/f005"
}],
"interpretation": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
"code": "L",
"display": "low"
}],
"text": "Below low normal"
}],
"component": [{
"code": {
"coding": [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "147842",
"display" : "MDC_ECG_HEART_RATE"
}]

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 17/29
},
"valueQuantity": {
"value": 43,
"unit": "beats/minute",
"system": "http://unitsofmeasure.org",
"code": "/min"
},
"interpretation": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
"code": "L",
"display": "low"
}],
"text": "Below low normal"
}],
"referenceRange": [{
"high": {
"value": 150,
"unit": "beats/minute",
"code": "/min"
},
"low": {
"value": 50,
"unit": "beats/minute",
"code": "/min"
}
}]
}]
}
Example End Alarm Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 18/29
{
"resourceType": "Observation",
"id": "206",
"status": "final",
"meta": {
"profile": ["http://hl7.org/fhir/StructureDefinition/vitalsigns"],
"tag": [{
"display": "extreme",
"code": "true"
},
{
"display": "acknowledged",
"code": "false"
}
]
},
"device" : {
"reference": "Device/351877479666121",
"display" : "HUB device"
},
"category": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/observation-category",
"code": "vital-signs",
"display": "Vital Signs"
}]
}],
"code": {
"coding": [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "147842",
"display" : "MDC_ECG_HEART_RATE"
}],
"text": "Heart rate"
},
"subject": {
"reference": "Patient/112215555"
},
"effectivePeriod": {
"start": "2012-09-17T13:03:51+00:00",
"end": "2012-09-17T16:20:45+00:00"
},
"performer": [{
"reference": "Practitioner/f005"
}],
"interpretation": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
"code": "L",
"display": "low"
}],
"text": "Below low normal"
}],
"component": [{
"code": {
"coding": [{
"system" : "urn:oid:2.16.840.1.113883.6.24",
"code" : "147842",
"display" : "MDC_ECG_HEART_RATE"
}]
},
"valueQuantity": {
"value": 41,
"unit": "beats/minute",

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 19/29
"system": "http://unitsofmeasure.org",
"code": "/min"
},
"interpretation": [{
"coding": [{
"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
"code": "L",
"display": "low"
}],
"text": "Below low normal"
}],
"referenceRange": [{
"high": {
"value": 150,
"unit": "beats/minute",
"code": "/min"
},
"low": {
"value": 50,
"unit": "beats/minute",
"code": "/min"
}
}]
}]
}
Example Success Response Messages
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f861",
"issue": [{
"severity": "information",
"details": {
"text": "Create alarm queued successfully"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f861",
"issue": [{
"severity": "information",
"details": {
"text": "Create HR vital and alarm queued successfully"
}
}]
}

Example Error Response Messages
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 20/29
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Unhandled event `{json data}'"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate device"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate patient"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Failed to create alarm HighHR"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "No operation to consume"
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 21/29
Parameters with Limit Alarms
NOTE: Any blood pressure message must include both systolic and diastolic values in a single message to be
processed as a valid alarm.
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 22/29
Parameter Name Numeric Range Spacelabs Alarm Sibel Alarm
HR 0-300 High HR High HR
Extreme High HR N/A *
Low HR Low HR
Extreme Low HR N/A *
RR 0-200 High Rate High RR
Low Rate Low RR
Temp 0-50 (Celsius) T1 High Body Temp High
T1 Low Body Temp Low
T2 High Other Temp High
T2 Low Other Temp Low
Systolic 30-260 High Systolic High Systolic
Extreme High Systolic N/A *
Low Systolic Low Systolic
Extreme Low Systolic N/A *
Diastolic 30-260 High Diastolic High Diastolic
Extreme High Diastolic N/A *
Low Diastolic Low Diastolic
Extreme Low Diastolic N/A *
SpO2 0-100 Sat High SPO2 High
Sat Low SPO2 Low
Pulse Rate (PR) 0-300 N/A PR High
N/A PR Low
Fall Count any N/A Fall Count High
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 23/29
Parameter Name Numeric Range Spacelabs Alarm Sibel Alarm
N/A Fall Count Low
Body Position any N/A Body Position High
N/A Body Position Low
Communication (Technical Alarms)
Name Comment
id Value of the identifier (a UUID). Required for message acknowledgment.
status Current status of alarm (preparation, in-progress, not-done, on-hold, stopped,
completed, entered-in-error, unknown). Required.
subject.reference Reference to a patient currently admitted into the SafeNSound system.
sent Timestamp when sent. Required.
sender.reference Reference to origin device. Required.
payload[].contentString
Content necessary for the function of the application integration is passed
through key/value pairs using this repeatable element. One payload element
with a contentString will start with either Error or Resolved . This signals the
type of technical alarm. Next will be a payload with a contentString prefixed
with Alert Priority , which is obvious in its purpose. Included is also a
payload with StartTime to match all end times of alarms to their correct
beginning event as well as a Type to tell which sensor triggered the alarm, and
finally, an ID value denoting the ID of the sensor.
* possible with SNS API
The communication resource is a record of a communication even if it is planned or has failed. A
communication is a record of the conveyance of information from one entity, a sender, to another entity, a
receiver. The sender and receivers may be patients, practitioners, related persons, organizations, or devices.
FHIR Resource: Communication https://www.hl7.org/fhir/communication.html
The following attributes are supported:
Example Start Alarm Request Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 24/29
{
"resourceType": "Communication",
"id": "3dc1d518-f07a-4b0c-b0a3-a4de16ff0af3",
"status": "inprogress",
"category": [
{
"coding": [
{
"system": "http://hl7.org/fhir/ValueSet/communication-category",
"code": "alert",
"display": "Alert"
}
],
"text": "Device Alert"
}
],
"subject": {
"reference": "Patient/4586787",
"display": "Test Bb"
},
"sent": "2025-01-21T11:03:30-06:00",
"sender": {
"reference": "Device/238795634122069"
},
"payload": [
{
"contentString": "Error: OutOfRange"
},
{
"contentString": "Alert Priority: LOW"
},
{
"contentString": "StartTime: 2025-01-21T11:03:30-06:00"
},
{
"contentString": "Type: anne-chest"
},
{
"contentString": "ID: CK1037"
}
]
}

Example End Alarm Request Message
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 25/29
{
"resourceType": "Communication",
"id": "28efebc6-bece-486f-9947-29793d3879ca",
"status": "completed",
"category": [
{
"coding": [
{
"system": "http://hl7.org/fhir/ValueSet/communication-category",
"code": "alert",
"display": "Alert"
}
],
"text": "Device Alert"
}
],
"subject": {
"reference": "Patient/4586787",
"display": "Test Bb"
},
"sent": "2025-01-21T11:03:43-06:00",
"sender": {
"reference": "Device/238795634122069"
},
"payload": [
{
"contentString": "Resolved: OutOfRange"
},
{
"contentString": "Alert Priority: LOW"
},
{
"contentString": "StartTime: 2025-01-21T11:03:30-06:00"
},
{
"contentString": "Type: anne-chest"
},
{
"contentString": "ID: CK1037"
}
]
}

Example Success Response Messages
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f861",
"issue": [{
"severity": "information",
"details": {
"text": "Create alarm queued successfully"
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 26/29
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f861",
"issue": [{
"severity": "information",
"details": {
"text": "Alarm queued successfully"
}
}]
}

Example Error Response Messages
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Unhandled event `{json data}'"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate device"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Cannot locate patient"
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 27/29
Technical Alarms
{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Failed to create alarm from message"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "Invalid Spacelabs alarm SomeWeirdAlarmName"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "No alarms found to process"
}
}]
}

{
"resourceType": "OperationOutcome",
"id": "15774c77-7a81-11ed-897b-0e0713e7f860",
"issue": [{
"severity": "error",
"details": {
"text": "No operation to consume"
}
}]
}

3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 28/29
Spacelabs Name Sibel Name
CriticalBattery CriticalBattery
ElectrodeOff ElectrodeOff
FingerNotDetected FingerNotDetected
LooseSleeve LooseSleeve
LowBattery LowBattery
LowSignalStrength LowSignalStrength
MovementDetected MovementDetected
OutOfRange OutOfRange
PoorSkinContact PoorSkinContact
SensorFault SensorFault
SystemError SystemError
WeakPulse WeakPulse
3/12/25, 10:48 AM RPM API - Overview
https://dev.azure.com/slhc/SNS/_wiki/wikis/SNS.wiki/2087/RPM-API 29/29