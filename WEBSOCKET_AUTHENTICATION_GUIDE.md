# WebSocket Authentication Guide

## The Issue You Discovered 🚨

You correctly identified that the README example was showing **incorrect** WebSocket authentication syntax:

```javascript
// ❌ WRONG - This doesn't work in practice
const ws = new WebSocket('ws://localhost:8080/api/v1/fhir/1234/************/store', [], {
  headers: {
    'Authorization': 'Bearer jwt-access-token'
  }
});
```

**Why this is wrong:**
- Browser WebSocket API doesn't support custom headers in the constructor
- The third parameter format shown doesn't exist in the WebSocket specification
- This would fail authentication with our JWT handshake interceptor

## How WebSocket Authentication Actually Works

### 1. **Android/Kotlin (Your App) - CORRECT** ✅

Your Android app likely uses OkHttp WebSocket which **does** support headers:

```kotlin
// This is how your SnsApiManager.kt probably works
val request = Request.Builder()
    .url("ws://localhost:8080/api/v1/fhir/$accountId/$deviceId/store")
    .addHeader("Authorization", "Bearer $jwtToken")
    .build()

val webSocket = okHttpClient.newWebSocket(request, webSocketListener)
```

### 2. **Server Side (What We Implemented)** ✅

Our `JwtHandshakeInterceptor` extracts the JWT token during the WebSocket handshake:

```java
// Extracts Authorization header during HTTP upgrade request
String authHeader = request.getHeaders().getFirst("Authorization");
if (authHeader != null && authHeader.startsWith("Bearer ")) {
    token = authHeader.substring(7);
}
```

### 3. **Browser JavaScript (Limited Options)**

**Option A: Query Parameter (for testing)**
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/fhir/1234/************/store?token=jwt-access-token');
```

**Option B: WebSocket Library with Header Support**
```javascript
// Using 'ws' library in Node.js
const WebSocket = require('ws');
const ws = new WebSocket('ws://localhost:8080/api/v1/fhir/1234/************/store', {
  headers: {
    'Authorization': 'Bearer jwt-access-token'
  }
});
```

## Server Enhancement Made

I updated the `JwtHandshakeInterceptor` to support **both** authentication methods:

1. **Primary**: Authorization header (for your Android app)
2. **Fallback**: Query parameter (for browser testing)

```java
// Try Authorization header first (preferred)
String authHeader = request.getHeaders().getFirst("Authorization");
if (authHeader != null && authHeader.startsWith("Bearer ")) {
    token = authHeader.substring(7);
} else {
    // Fallback: Extract from query parameter
    String queryString = request.getURI().getQuery();
    if (queryString != null && queryString.contains("token=")) {
        // Parse token from query string
    }
}
```

## Testing Your Android App

Your Android app should work perfectly with the server as-is because:

1. ✅ **Correct URL Pattern**: `/api/v1/fhir/{account}/{deviceId}/store`
2. ✅ **JWT Header Support**: Server reads `Authorization: Bearer {token}` header
3. ✅ **FHIR Message Simulation**: Server sends Patient and OperationOutcome messages
4. ✅ **Message Filtering**: Messages include `resourceType` field for client-side routing

## Connection Flow

```
Android App                          Spring Boot Server
     |                                       |
     |  WebSocket Upgrade Request            |
     |  Authorization: Bearer {jwt}          |
     |-------------------------------------->|
     |                                       |
     |  JwtHandshakeInterceptor validates    |
     |  token and extracts account/device    |
     |                                       |
     |  WebSocket Connection Established     |
     |<--------------------------------------|
     |                                       |
     |  Initial Patient Resource (2s delay)  |
     |<--------------------------------------|
     |                                       |
     |  Send FHIR Observation                |
     |-------------------------------------->|
     |                                       |
     |  OperationOutcome Success Response    |
     |<--------------------------------------|
     |                                       |
     |  Periodic Patient Updates (30s)       |
     |<--------------------------------------|
```

## Key Takeaway

Your understanding is correct - the README example was misleading. WebSocket authentication works differently than regular HTTP requests, and your Android app's approach using OkHttp with proper headers is the right way to do it.
