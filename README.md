# Medical Dummy Server

A simple Spring Boot-based mock server for testing Android applications locally. This server accepts any JSON data via REST API and WebSocket endpoints, stores it in PostgreSQL, and returns basic success/error responses.

## 🎯 Purpose

This is a **dummy/mock server** designed specifically for:
- **Local testing** of Android medical applications
- **Development environment** data storage and retrieval
- **Simple JSON data persistence** without complex validation
- **Basic authentication testing** with JWT tokens

## ✨ Features

- **Simple JSON Storage**: Accepts and stores any JSON data without validation
- **REST API Endpoints**: Device association, JWT token generation, and data storage
- **WebSocket Support**: Real-time JSON message storage
- **PostgreSQL Database**: Persistent data storage
- **JWT Authentication**: Basic token-based authentication for testing
- **Data Retrieval**: Simple endpoints to view stored data for verification
- **No External Dependencies**: Completely standalone server

## 🛠️ Prerequisites

- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+ (running locally)

## 🚀 Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd medicalDummyServer
   ```

2. **Setup PostgreSQL Database**
   ```sql
   -- Create database
   CREATE DATABASE medical_dummy_db;

   -- Create user (if needed)
   CREATE USER postgres WITH PASSWORD 'postgres';
   GRANT ALL PRIVILEGES ON DATABASE medical_dummy_db TO postgres;
   ```

3. **Configure database** (Optional)
   The server is pre-configured for PostgreSQL with:
   - **Host**: localhost:5432
   - **Database**: medical_dummy_db
   - **Username**: postgres
   - **Password**: postgres

   Update `src/main/resources/application.properties` if your setup is different.

4. **Run the application**
   ```bash
   ./mvnw spring-boot:run
   ```

The server will start on `http://localhost:8080`

## 📊 Database Schema

The server automatically creates these tables:
- **json_messages**: Stores all received JSON data
- **device_associations**: Device-to-account associations
- **patients**: Sample patient data
- **devices**: Sample device data

## 📡 API Endpoints

### 1. Device Association
```
GET /api/v1/associate/{account}/{deviceId}
```
Associates a device with an organization account.

**Example:**
```bash
curl -X GET "http://localhost:8080/api/v1/associate/1234/************"
```

**Response:**
```json
{
  "name": "FHIR",
  "version": "1",
  "payload": {
    "authToken": "base64-encoded-token",
    "loginUrl": "http://localhost:8080",
    "baseUri": "/oauth/token",
    "accountNumber": "1234"
  }
}
```

### 2. JWT Token Generation
```
POST /oauth/token
```
Generates JWT access and refresh tokens.

**Example:**
```bash
curl -X POST "http://localhost:8080/oauth/token?accountNumber=1234&username=hubapi&Origin=EMR&Token=sample" \
  -H "Authorization: Basic base64-encoded-credentials"
```

**Response:**
```json
{
  "role": "HUB",
  "liveViewOnly": true,
  "mustChangePassword": "false",
  "themeMode": "LIGHT",
  "token_type": "Bearer",
  "accountNumber": "1234",
  "userName": "Hub Api User",
  "userId": 20950,
  "access_token": "jwt-access-token",
  "organizationId": 768,
  "refresh_token": "jwt-refresh-token",
  "scope": "HUB HUB_API",
  "pinLength": 6,
  "expires_in": 86398,
  "passcode": "null"
}
```

### 3. Data Storage Endpoint
```
POST /api/data/{account}/{deviceId}
```
Accepts any JSON data and stores it in the database.

**Example:**
```bash
curl -X POST "http://localhost:8080/api/data/1234/device001" \
  -H "Content-Type: application/json" \
  -d '{"heartRate": 72, "timestamp": "2025-01-09T10:30:00"}'
```

### 4. WebSocket FHIR Endpoint
```
WebSocket: /api/v1/fhir/{account}/{deviceId}/store
```
Real-time FHIR message communication via WebSocket with SNS simulation features.

**Connection:**
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/fhir/1234/************/store', [], {
  headers: {
    'Authorization': 'Bearer jwt-access-token'
  }
});
```

**FHIR Simulation Features:**
- **Automatic Patient Resource Updates**: Server sends periodic Patient resources to test client-side filtering
- **FHIR OperationOutcome Responses**: Proper FHIR responses for Observation data
- **Resource Type Filtering**: Messages include `resourceType` field for client-side routing
- **Configurable Timing**: Adjustable intervals for Patient updates and response timing

## Data Retrieval Endpoints

### Get All Messages
```
GET /api/data/messages
```

### Get Messages by Account
```
GET /api/data/messages/account/{account}
```

### Get Messages by Account and Device
```
GET /api/data/messages/account/{account}/device/{deviceId}
```

### Get Statistics
```
GET /api/data/statistics
```

## 🧪 Testing

### 1. Interactive Web Interface
Open `websocket-test.html` in your browser for interactive testing:
```bash
# Open the test page
open websocket-test.html
```

### 2. Command Line Testing

**Send sample medical data:**
```bash
curl -X POST "http://localhost:8080/api/data/1234/device001" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "vitals",
    "patient": {"id": "123", "name": "John Doe"},
    "data": {
      "heartRate": 72,
      "bloodPressure": {"systolic": 120, "diastolic": 80},
      "temperature": 98.6,
      "oxygenSaturation": 98
    },
    "timestamp": "2025-07-09T12:00:00"
  }'
```

**Send alert data:**
```bash
curl -X POST "http://localhost:8080/api/data/1234/device001" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "alert",
    "severity": "HIGH",
    "message": "Heart rate above threshold",
    "patient": {"id": "123"},
    "device": {"id": "device001"},
    "timestamp": "2025-07-09T12:00:00"
  }'
```

**Retrieve stored data:**
```bash
# Get all messages
curl -X GET "http://localhost:8080/api/data/messages"

# Get messages for specific account
curl -X GET "http://localhost:8080/api/data/messages/account/1234"

# Get statistics
curl -X GET "http://localhost:8080/api/data/statistics"
```

## 🏗️ Project Structure

```
src/main/java/in/lakshay/medicalDummyServer/
├── controller/
│   ├── AuthorizationController.java    # Device association endpoint
│   ├── OAuthController.java           # JWT token generation
│   ├── DataController.java            # Main data storage endpoints
│   └── TestController.java            # Health check and test endpoints
├── entity/
│   ├── JsonMessage.java               # Main entity for storing JSON data
│   ├── DeviceAssociation.java         # Device associations
│   ├── Patient.java                   # Sample patient data
│   └── Device.java                    # Sample device data
├── service/
│   ├── JsonMessageService.java        # Core data storage service
│   └── DataInitializationService.java # Sample data initialization
├── repository/
│   └── JsonMessageRepository.java     # Data access layer
├── websocket/
│   └── FhirWebSocketHandler.java      # WebSocket message handling
└── config/
    ├── SecurityConfig.java            # Security configuration
    └── WebSocketConfig.java           # WebSocket configuration
```

## 🔑 Key Points

- **Simple & Lightweight**: No complex FHIR validation or processing
- **Any JSON Accepted**: Stores any valid JSON data without schema validation
- **PostgreSQL Storage**: Persistent data storage with automatic table creation
- **Local Testing**: Designed for local development and testing only
- **Basic Authentication**: Simple JWT token generation for testing auth flows
- **WebSocket Support**: Real-time data reception via WebSocket connections
- **Data Retrieval**: Easy endpoints to view and verify stored data

## 🚨 Important Notes

- This is a **mock server** for testing purposes only
- **No production use** - lacks security hardening and validation
- **No data validation** - accepts any JSON payload
- **Local development** - designed for localhost testing
- **Sample data** is automatically created on startup

## 🛠️ Development

To run tests:
```bash
./mvnw test
```

To build the project:
```bash
./mvnw clean package
```

## 📝 License

This project is for development and testing purposes.

## Setup Instructions

### Prerequisites
- Java 17 or higher
- PostgreSQL database
- Maven 3.6+

### Database Setup
1. Create a PostgreSQL database:
```sql
CREATE DATABASE medical_dummy_db;
```

2. Update `src/main/resources/application.properties`:
```properties
spring.datasource.url=*************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### Running the Application
1. Clone the repository
2. Configure the database connection in `application.properties`
3. Run the application:
```bash
./mvnw spring-boot:run
```

The server will start on `http://localhost:8080`

### Testing
Run the tests:
```bash
./mvnw test
```

## Test Endpoints

### Health Check
```
GET /api/test/health
```

### FHIR Testing Endpoints
```
POST /api/test/fhir/send-patient/{account}/{deviceId}
POST /api/test/fhir/send-operation-outcome/{account}/{deviceId}?severity=information&message=Test
GET /api/test/fhir/sample-observation
```

### Sample Data
```
GET /api/test/sample-observation
GET /api/test/sample-communication
```

### Data Retrieval
```
GET /api/test/observations
GET /api/test/observations/account/{accountNumber}
GET /api/test/communications
```

## Sample Data

The application automatically creates sample data on startup:
- Device associations for accounts 1234, 2000, 5678
- Sample patients and devices
- Ready-to-use test data for development

## Configuration

Key configuration properties in `application.properties`:

```properties
# Server
server.port=8080

# Database
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=password

# JWT
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=********

# WebSocket
websocket.allowed-origins=*

# FHIR Simulation
fhir.simulation.patient-update-interval-seconds=30
fhir.simulation.initial-patient-delay-seconds=2
fhir.simulation.enable-patient-updates=true
fhir.simulation.enable-fhir-responses=true

# FHIR
fhir.base-url=https://rpm.510.test.safensound.io
```

## Architecture

- **Controllers**: Handle HTTP requests and WebSocket connections
- **Services**: Business logic for FHIR message processing and JWT handling
- **Repositories**: Data access layer using Spring Data JPA
- **Entities**: JPA entities for database mapping
- **DTOs**: Data transfer objects for API responses

## Error Handling

The server returns FHIR-compliant OperationOutcome responses for errors:

```json
{
  "resourceType": "OperationOutcome",
  "id": "message-id",
  "issue": [{
    "severity": "error",
    "details": {
      "text": "Error description"
    }
  }]
}
```

## Contributing

1. Follow the existing code style (single-line comments only)
2. Add tests for new functionality
3. Update documentation as needed

## License

This project is for educational and development purposes.
