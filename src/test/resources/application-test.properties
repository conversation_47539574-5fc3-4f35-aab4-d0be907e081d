# Test Database Configuration (H2)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate Configuration for Tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=false

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# WebSocket Configuration
websocket.allowed-origins=*

# Logging Configuration
logging.level.in.lakshay.medicalDummyServer=WARN
logging.level.org.springframework.web.socket=WARN
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN
