package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.Patient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PatientRepository extends JpaRepository<Patient, Long> {
    
    Optional<Patient> findByPatientReferenceAndIsActive(String patientReference, Boolean isActive);
    
    Optional<Patient> findByPatientReferenceAndAccountNumberAndIsActive(
        String patientReference, String accountNumber, Boolean isActive);
    
    boolean existsByPatientReferenceAndAccountNumberAndIsActive(
        String patientReference, String accountNumber, Boolean isActive);
}
