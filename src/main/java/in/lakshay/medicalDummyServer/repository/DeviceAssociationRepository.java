package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.DeviceAssociation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DeviceAssociationRepository extends JpaRepository<DeviceAssociation, Long> {
    
    Optional<DeviceAssociation> findByAccountNumberAndDeviceIdAndIsActive(
        String accountNumber, String deviceId, Boolean isActive);
    
    Optional<DeviceAssociation> findByDeviceIdAndIsActive(String deviceId, Boolean isActive);
    
    boolean existsByAccountNumberAndDeviceIdAndIsActive(
        String accountNumber, String deviceId, Boolean isActive);
}
