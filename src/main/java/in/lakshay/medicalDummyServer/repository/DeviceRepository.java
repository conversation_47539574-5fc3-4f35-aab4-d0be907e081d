package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DeviceRepository extends JpaRepository<Device, Long> {
    
    Optional<Device> findByDeviceReferenceAndIsActive(String deviceReference, Boolean isActive);
    
    Optional<Device> findByDeviceUuidAndIsActive(String deviceUuid, Boolean isActive);
    
    Optional<Device> findByDeviceReferenceAndAccountNumberAndIsActive(
        String deviceReference, String accountNumber, Boolean isActive);
    
    Optional<Device> findByDeviceUuidAndAccountNumberAndIsActive(
        String deviceUuid, String accountNumber, Boolean isActive);
    
    boolean existsByDeviceReferenceAndAccountNumberAndIsActive(
        String deviceReference, String accountNumber, Boolean isActive);
    
    boolean existsByDeviceUuidAndAccountNumberAndIsActive(
        String deviceUuid, String accountNumber, Boolean isActive);
}
