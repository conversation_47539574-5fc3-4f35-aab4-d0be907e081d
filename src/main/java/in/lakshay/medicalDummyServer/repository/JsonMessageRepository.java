package in.lakshay.medicalDummyServer.repository;

import in.lakshay.medicalDummyServer.entity.JsonMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface JsonMessageRepository extends JpaRepository<JsonMessage, Long> {
    
    List<JsonMessage> findByAccountNumber(String accountNumber);
    
    List<JsonMessage> findByAccountNumberAndDeviceId(String accountNumber, String deviceId);
    
    List<JsonMessage> findByMessageType(String messageType);
    
    List<JsonMessage> findByEndpoint(String endpoint);
    
    @Query("SELECT j FROM JsonMessage j WHERE j.accountNumber = :accountNumber " +
           "AND j.createdAt BETWEEN :startTime AND :endTime ORDER BY j.createdAt DESC")
    List<JsonMessage> findByAccountNumberAndCreatedAtBetween(
        @Param("accountNumber") String accountNumber,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT j FROM JsonMessage j WHERE j.accountNumber = :accountNumber " +
           "AND j.deviceId = :deviceId ORDER BY j.createdAt DESC")
    List<JsonMessage> findByAccountNumberAndDeviceIdOrderByCreatedAtDesc(
        @Param("accountNumber") String accountNumber,
        @Param("deviceId") String deviceId);
    
    @Query("SELECT j FROM JsonMessage j ORDER BY j.createdAt DESC")
    List<JsonMessage> findAllOrderByCreatedAtDesc();
    
    long countByAccountNumber(String accountNumber);
    
    long countByAccountNumberAndDeviceId(String accountNumber, String deviceId);
}
