package in.lakshay.medicalDummyServer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JwtTokenResponse {
    
    private String role = "HUB";
    
    @JsonProperty("liveViewOnly")
    private boolean liveViewOnly = true;
    
    @JsonProperty("mustChangePassword")
    private String mustChangePassword = "false";
    
    @JsonProperty("themeMode")
    private String themeMode = "LIGHT";
    
    @JsonProperty("token_type")
    private String tokenType = "Bearer";
    
    @JsonProperty("accountNumber")
    private String accountNumber;
    
    @JsonProperty("userName")
    private String userName = "Hub Api User";
    
    @JsonProperty("userId")
    private Long userId = 20950L;
    
    @JsonProperty("access_token")
    private String accessToken;
    
    @JsonProperty("organizationId")
    private Long organizationId = 768L;
    
    @JsonProperty("refresh_token")
    private String refreshToken;
    
    private String scope = "HUB HUB_API";
    
    @JsonProperty("pinLength")
    private int pinLength = 6;
    
    @JsonProperty("expires_in")
    private long expiresIn;
    
    private String passcode = "null";
    
    public JwtTokenResponse(String accountNumber, String accessToken, String refreshToken, long expiresIn) {
        this.accountNumber = accountNumber;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresIn = expiresIn;
    }
}
