package in.lakshay.medicalDummyServer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthorizationResponse {
    
    private String name = "FHIR";
    private String version = "1";
    private AuthorizationPayload payload;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AuthorizationPayload {
        private String authToken;
        private String loginUrl;
        private String baseUri;
        private String accountNumber;
        private String errorMessage;
        
        // Success constructor
        public AuthorizationPayload(String authToken, String loginUrl, String baseUri, String accountNumber) {
            this.authToken = authToken;
            this.loginUrl = loginUrl;
            this.baseUri = baseUri;
            this.accountNumber = accountNumber;
        }
        
        // Error constructor
        public AuthorizationPayload(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
    
    // Success response constructor
    public AuthorizationResponse(String authToken, String loginUrl, String baseUri, String accountNumber) {
        this.name = "FHIR";
        this.version = "1";
        this.payload = new AuthorizationPayload(authToken, loginUrl, baseUri, accountNumber);
    }
    
    // Error response constructor
    public AuthorizationResponse(String errorMessage) {
        this.name = "FHIR";
        this.version = "1";
        this.payload = new AuthorizationPayload(errorMessage);
    }
}
