package in.lakshay.medicalDummyServer.service;

import in.lakshay.medicalDummyServer.entity.JsonMessage;
import in.lakshay.medicalDummyServer.repository.JsonMessageRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
public class JsonMessageService {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonMessageService.class);
    
    @Autowired
    private JsonMessageRepository jsonMessageRepository;
    
    public JsonMessage saveMessage(String accountNumber, String deviceId, String messageType, 
                                   String endpoint, String jsonData, String clientIp, String userAgent) {
        try {
            JsonMessage message = new JsonMessage();
            message.setAccountNumber(accountNumber);
            message.setDeviceId(deviceId);
            message.setMessageType(messageType);
            message.setEndpoint(endpoint);
            message.setJsonData(jsonData);
            message.setClientIp(clientIp);
            message.setUserAgent(userAgent);
            
            JsonMessage saved = jsonMessageRepository.save(message);
            logger.info("Saved message ID: {} for account: {} and device: {}", 
                       saved.getId(), accountNumber, deviceId);
            return saved;
            
        } catch (Exception e) {
            logger.error("Error saving message for account: {} and device: {}", accountNumber, deviceId, e);
            throw new RuntimeException("Failed to save message", e);
        }
    }
    
    public List<JsonMessage> getAllMessages() {
        return jsonMessageRepository.findAllOrderByCreatedAtDesc();
    }
    
    public List<JsonMessage> getMessagesByAccount(String accountNumber) {
        return jsonMessageRepository.findByAccountNumber(accountNumber);
    }
    
    public List<JsonMessage> getMessagesByAccountAndDevice(String accountNumber, String deviceId) {
        return jsonMessageRepository.findByAccountNumberAndDeviceIdOrderByCreatedAtDesc(accountNumber, deviceId);
    }
    
    public List<JsonMessage> getMessagesByType(String messageType) {
        return jsonMessageRepository.findByMessageType(messageType);
    }
    
    public List<JsonMessage> getMessagesByEndpoint(String endpoint) {
        return jsonMessageRepository.findByEndpoint(endpoint);
    }
    
    public List<JsonMessage> getMessagesByAccountAndTimeRange(String accountNumber, 
                                                              LocalDateTime startTime, 
                                                              LocalDateTime endTime) {
        return jsonMessageRepository.findByAccountNumberAndCreatedAtBetween(accountNumber, startTime, endTime);
    }
    
    public Map<String, Object> getStatistics() {
        long totalMessages = jsonMessageRepository.count();
        
        return Map.of(
            "totalMessages", totalMessages,
            "messagesByType", Map.of(
                "REST", jsonMessageRepository.findByMessageType("REST").size(),
                "WEBSOCKET", jsonMessageRepository.findByMessageType("WEBSOCKET").size()
            ),
            "timestamp", LocalDateTime.now()
        );
    }
    
    public Map<String, Object> getAccountStatistics(String accountNumber) {
        long totalMessages = jsonMessageRepository.countByAccountNumber(accountNumber);
        List<JsonMessage> recentMessages = jsonMessageRepository.findByAccountNumber(accountNumber);
        
        return Map.of(
            "accountNumber", accountNumber,
            "totalMessages", totalMessages,
            "recentMessagesCount", Math.min(recentMessages.size(), 10),
            "timestamp", LocalDateTime.now()
        );
    }
    
    public void deleteAllMessages() {
        jsonMessageRepository.deleteAll();
        logger.info("Deleted all messages");
    }
    
    public void deleteMessagesByAccount(String accountNumber) {
        List<JsonMessage> messages = jsonMessageRepository.findByAccountNumber(accountNumber);
        jsonMessageRepository.deleteAll(messages);
        logger.info("Deleted {} messages for account: {}", messages.size(), accountNumber);
    }
}
