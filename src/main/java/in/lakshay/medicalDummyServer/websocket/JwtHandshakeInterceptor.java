package in.lakshay.medicalDummyServer.websocket;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import in.lakshay.medicalDummyServer.service.JwtService;

@Component
public class JwtHandshakeInterceptor implements HandshakeInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtHandshakeInterceptor.class);
    
    @Autowired
    private JwtService jwtService;
    
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        
        logger.info("WebSocket handshake attempt from: {}", request.getRemoteAddress());
        
        try {
            String token = null;

            // Try to extract Authorization header first (preferred method)
            String authHeader = request.getHeaders().getFirst("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            } else {
                // Fallback: Try to extract token from query parameter (for browser testing)
                String queryString = request.getURI().getQuery();
                if (queryString != null && queryString.contains("token=")) {
                    String[] params = queryString.split("&");
                    for (String param : params) {
                        if (param.startsWith("token=")) {
                            token = param.substring(6);
                            break;
                        }
                    }
                }
            }

            if (token == null) {
                logger.warn("Missing JWT token in Authorization header or query parameter");
                return false;
            }
            
            // Extract account and deviceId from URL path
            String path = request.getURI().getPath();
            String[] pathParts = path.split("/");
            
            if (pathParts.length < 6) {
                logger.warn("Invalid WebSocket path format: {}", path);
                return false;
            }
            
            String account = pathParts[4]; // /api/v1/fhir/{account}/{deviceId}/store
            String deviceId = pathParts[5];
            
            // Validate JWT token
            if (!jwtService.validateToken(token)) {
                logger.warn("Invalid JWT token");
                return false;
            }
            
            // Validate account number matches token
            String tokenAccount = jwtService.extractAccountNumber(token);
            if (!account.equals(tokenAccount)) {
                logger.warn("Account mismatch: URL={}, Token={}", account, tokenAccount);
                return false;
            }
            
            // Store validated information in session attributes
            attributes.put("accountNumber", account);
            attributes.put("deviceId", deviceId);
            attributes.put("userId", jwtService.extractUserId(token));
            attributes.put("username", jwtService.extractUsername(token));
            
            logger.info("WebSocket handshake successful for account: {} and device: {}", account, deviceId);
            return true;
            
        } catch (Exception e) {
            logger.error("Error during WebSocket handshake", e);
            return false;
        }
    }
    
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            logger.error("WebSocket handshake failed", exception);
        } else {
            logger.info("WebSocket handshake completed successfully");
        }
    }
}
