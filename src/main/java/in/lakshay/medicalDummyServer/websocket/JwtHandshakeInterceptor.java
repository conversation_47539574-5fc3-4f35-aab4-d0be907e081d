package in.lakshay.medicalDummyServer.websocket;

import in.lakshay.medicalDummyServer.service.JwtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

@Component
public class JwtHandshakeInterceptor implements HandshakeInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtHandshakeInterceptor.class);
    
    @Autowired
    private JwtService jwtService;
    
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        
        logger.info("WebSocket handshake attempt from: {}", request.getRemoteAddress());
        
        try {
            // Extract Authorization header
            String authHeader = request.getHeaders().getFirst("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                logger.warn("Missing or invalid Authorization header");
                return false;
            }
            
            String token = authHeader.substring(7);
            
            // Extract account and deviceId from URL path
            String path = request.getURI().getPath();
            String[] pathParts = path.split("/");
            
            if (pathParts.length < 6) {
                logger.warn("Invalid WebSocket path format: {}", path);
                return false;
            }
            
            String account = pathParts[4]; // /api/v1/fhir/{account}/{deviceId}/store
            String deviceId = pathParts[5];
            
            // Validate JWT token
            if (!jwtService.validateToken(token)) {
                logger.warn("Invalid JWT token");
                return false;
            }
            
            // Validate account number matches token
            String tokenAccount = jwtService.extractAccountNumber(token);
            if (!account.equals(tokenAccount)) {
                logger.warn("Account mismatch: URL={}, Token={}", account, tokenAccount);
                return false;
            }
            
            // Store validated information in session attributes
            attributes.put("accountNumber", account);
            attributes.put("deviceId", deviceId);
            attributes.put("userId", jwtService.extractUserId(token));
            attributes.put("username", jwtService.extractUsername(token));
            
            logger.info("WebSocket handshake successful for account: {} and device: {}", account, deviceId);
            return true;
            
        } catch (Exception e) {
            logger.error("Error during WebSocket handshake", e);
            return false;
        }
    }
    
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            logger.error("WebSocket handshake failed", exception);
        } else {
            logger.info("WebSocket handshake completed successfully");
        }
    }
}
