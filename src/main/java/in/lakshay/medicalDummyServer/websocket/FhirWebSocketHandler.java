package in.lakshay.medicalDummyServer.websocket;

import in.lakshay.medicalDummyServer.service.JsonMessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.util.concurrent.ConcurrentHashMap;

@Component
public class FhirWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(FhirWebSocketHandler.class);

    @Autowired
    private JsonMessageService jsonMessageService;

    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        String sessionKey = accountNumber + ":" + deviceId;
        
        sessions.put(sessionKey, session);
        logger.info("WebSocket connection established for account: {} and device: {}", accountNumber, deviceId);
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");

        logger.info("Received WebSocket message from account: {} and device: {}", accountNumber, deviceId);

        try {
            String payload = message.getPayload().toString();
            logger.debug("Message payload: {}", payload);

            // Store the message without any processing
            String endpoint = "/api/v1/fhir/" + accountNumber + "/" + deviceId + "/store";
            jsonMessageService.saveMessage(accountNumber, deviceId, "WEBSOCKET", endpoint, payload, null, null);

            // Send simple success response
            String successResponse = String.format(
                "{\"status\":\"success\",\"message\":\"Data received and stored successfully\",\"timestamp\":\"%s\"}",
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );

            session.sendMessage(new TextMessage(successResponse));
            logger.info("Stored WebSocket message and sent success response");

        } catch (Exception e) {
            logger.error("Error processing WebSocket message", e);
            String errorResponse = String.format(
                "{\"status\":\"error\",\"message\":\"Failed to process data: %s\",\"timestamp\":\"%s\"}",
                e.getMessage(),
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );
            session.sendMessage(new TextMessage(errorResponse));
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        
        logger.error("WebSocket transport error for account: {} and device: {}", accountNumber, deviceId, exception);
        
        // Close session on transport error
        if (session.isOpen()) {
            session.close(CloseStatus.SERVER_ERROR);
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        String sessionKey = accountNumber + ":" + deviceId;
        
        sessions.remove(sessionKey);
        logger.info("WebSocket connection closed for account: {} and device: {}. Status: {}", 
                   accountNumber, deviceId, closeStatus);
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    // Utility method to get active session for account/device
    public WebSocketSession getSession(String accountNumber, String deviceId) {
        return sessions.get(accountNumber + ":" + deviceId);
    }
    
    // Utility method to send message to specific session
    public void sendMessageToSession(String accountNumber, String deviceId, String message) {
        WebSocketSession session = getSession(accountNumber, deviceId);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
                logger.info("Sent message to account: {} and device: {}", accountNumber, deviceId);
            } catch (Exception e) {
                logger.error("Error sending message to WebSocket session", e);
            }
        } else {
            logger.warn("No active session found for account: {} and device: {}", accountNumber, deviceId);
        }
    }
}
