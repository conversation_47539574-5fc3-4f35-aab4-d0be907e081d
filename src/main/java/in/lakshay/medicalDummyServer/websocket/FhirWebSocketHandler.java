package in.lakshay.medicalDummyServer.websocket;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.lakshay.medicalDummyServer.config.FhirSimulationConfig;
import in.lakshay.medicalDummyServer.service.JsonMessageService;

@Component
public class FhirWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(FhirWebSocketHandler.class);

    @Autowired
    private JsonMessageService jsonMessageService;

    @Autowired
    private FhirSimulationConfig fhirConfig;

    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    // FHIR message templates
    private static final String PATIENT_RESOURCE_TEMPLATE = """
        {
            "resourceType": "Patient",
            "id": "%s",
            "meta": {
                "versionId": "1",
                "lastUpdated": "%s"
            },
            "identifier": [
                {
                    "use": "usual",
                    "system": "http://spacelabs.com/patient-id",
                    "value": "%s"
                }
            ],
            "active": true,
            "name": [
                {
                    "use": "official",
                    "family": "TestPatient",
                    "given": ["Device", "Test"]
                }
            ],
            "gender": "unknown",
            "birthDate": "1990-01-01"
        }
        """;

    private static final String OPERATION_OUTCOME_SUCCESS_TEMPLATE = """
        {
            "resourceType": "OperationOutcome",
            "id": "%s",
            "meta": {
                "lastUpdated": "%s"
            },
            "issue": [
                {
                    "severity": "information",
                    "code": "informational",
                    "details": {
                        "text": "FHIR Observation data received and processed successfully"
                    }
                }
            ]
        }
        """;

    private static final String OPERATION_OUTCOME_ERROR_TEMPLATE = """
        {
            "resourceType": "OperationOutcome",
            "id": "%s",
            "meta": {
                "lastUpdated": "%s"
            },
            "issue": [
                {
                    "severity": "error",
                    "code": "processing",
                    "details": {
                        "text": "Error processing FHIR data: %s"
                    }
                }
            ]
        }
        """;
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        String sessionKey = accountNumber + ":" + deviceId;

        sessions.put(sessionKey, session);
        logger.info("WebSocket connection established for account: {} and device: {}", accountNumber, deviceId);

        // Start periodic Patient resource updates to simulate SNS server behavior
        if (fhirConfig.isEnablePatientUpdates()) {
            startPeriodicPatientUpdates(accountNumber, deviceId);

            // Send initial Patient resource after a configurable delay
            scheduler.schedule(() -> {
                sendPatientResource(accountNumber, deviceId);
            }, fhirConfig.getInitialPatientDelaySeconds(), TimeUnit.SECONDS);
        }
    }
    
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");

        logger.info("Received WebSocket message from account: {} and device: {}", accountNumber, deviceId);

        try {
            String payload = message.getPayload().toString();
            logger.debug("Message payload: {}", payload);

            // Store the message
            String endpoint = "/api/v1/fhir/" + accountNumber + "/" + deviceId + "/store";
            jsonMessageService.saveMessage(accountNumber, deviceId, "WEBSOCKET", endpoint, payload, null, null);

            // Check if this is a FHIR Observation and send appropriate OperationOutcome
            if (fhirConfig.isEnableFhirResponses() && isFhirObservation(payload)) {
                sendOperationOutcomeSuccess(session, accountNumber, deviceId);
                logger.info("Sent FHIR OperationOutcome success response for Observation");
            } else {
                // Send generic success response for non-FHIR messages
                sendGenericSuccessResponse(session);
                logger.info("Sent generic success response");
            }

        } catch (Exception e) {
            logger.error("Error processing WebSocket message", e);
            sendOperationOutcomeError(session, e.getMessage());
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        
        logger.error("WebSocket transport error for account: {} and device: {}", accountNumber, deviceId, exception);
        
        // Close session on transport error
        if (session.isOpen()) {
            session.close(CloseStatus.SERVER_ERROR);
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String accountNumber = (String) session.getAttributes().get("accountNumber");
        String deviceId = (String) session.getAttributes().get("deviceId");
        String sessionKey = accountNumber + ":" + deviceId;

        sessions.remove(sessionKey);
        logger.info("WebSocket connection closed for account: {} and device: {}. Status: {}",
                   accountNumber, deviceId, closeStatus);

        // Note: Scheduler tasks will automatically stop when session is removed
        // since sendMessageToSession checks if session is open
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    // Utility method to get active session for account/device
    public WebSocketSession getSession(String accountNumber, String deviceId) {
        return sessions.get(accountNumber + ":" + deviceId);
    }
    
    // Utility method to send message to specific session
    public void sendMessageToSession(String accountNumber, String deviceId, String message) {
        WebSocketSession session = getSession(accountNumber, deviceId);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
                logger.info("Sent message to account: {} and device: {}", accountNumber, deviceId);
            } catch (Exception e) {
                logger.error("Error sending message to WebSocket session", e);
            }
        } else {
            logger.warn("No active session found for account: {} and device: {}", accountNumber, deviceId);
        }
    }

    // Enhanced methods for FHIR simulation

    private void startPeriodicPatientUpdates(String accountNumber, String deviceId) {
        // Send Patient resource updates at configurable intervals to simulate real SNS behavior
        int intervalSeconds = fhirConfig.getPatientUpdateIntervalSeconds();
        scheduler.scheduleAtFixedRate(() -> {
            sendPatientResource(accountNumber, deviceId);
        }, intervalSeconds, intervalSeconds, TimeUnit.SECONDS);

        logger.info("Started periodic Patient resource updates for account: {} device: {} (interval: {}s)",
                   accountNumber, deviceId, intervalSeconds);
    }

    private void sendPatientResource(String accountNumber, String deviceId) {
        try {
            String patientId = "patient-" + deviceId;
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String patientIdentifier = accountNumber + "-" + deviceId;

            String patientMessage = String.format(PATIENT_RESOURCE_TEMPLATE,
                patientId, timestamp, patientIdentifier);

            sendMessageToSession(accountNumber, deviceId, patientMessage);
            logger.debug("Sent Patient resource update for device: {}", deviceId);

        } catch (Exception e) {
            logger.error("Error sending Patient resource", e);
        }
    }

    private boolean isFhirObservation(String payload) {
        try {
            JsonNode jsonNode = objectMapper.readTree(payload);
            String resourceType = jsonNode.path("resourceType").asText();
            return "Observation".equals(resourceType);
        } catch (Exception e) {
            logger.debug("Failed to parse message as JSON or extract resourceType", e);
            return false;
        }
    }

    private void sendOperationOutcomeSuccess(WebSocketSession session, String accountNumber, String deviceId) {
        try {
            String outcomeId = "outcome-" + UUID.randomUUID().toString();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

            String operationOutcome = String.format(OPERATION_OUTCOME_SUCCESS_TEMPLATE,
                outcomeId, timestamp);

            session.sendMessage(new TextMessage(operationOutcome));
            logger.debug("Sent OperationOutcome success for account: {} device: {}", accountNumber, deviceId);

        } catch (Exception e) {
            logger.error("Error sending OperationOutcome success", e);
        }
    }

    private void sendOperationOutcomeError(WebSocketSession session, String errorMessage) {
        try {
            String outcomeId = "outcome-error-" + UUID.randomUUID().toString();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

            String operationOutcome = String.format(OPERATION_OUTCOME_ERROR_TEMPLATE,
                outcomeId, timestamp, errorMessage);

            session.sendMessage(new TextMessage(operationOutcome));
            logger.debug("Sent OperationOutcome error: {}", errorMessage);

        } catch (Exception e) {
            logger.error("Error sending OperationOutcome error", e);
        }
    }

    private void sendGenericSuccessResponse(WebSocketSession session) {
        try {
            String successResponse = String.format(
                "{\"status\":\"success\",\"message\":\"Data received and stored successfully\",\"timestamp\":\"%s\"}",
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );

            session.sendMessage(new TextMessage(successResponse));

        } catch (Exception e) {
            logger.error("Error sending generic success response", e);
        }
    }
}
