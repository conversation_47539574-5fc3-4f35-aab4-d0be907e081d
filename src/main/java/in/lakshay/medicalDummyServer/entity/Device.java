package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "devices")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Device {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "device_reference", nullable = false, unique = true)
    private String deviceReference; // e.g., "Device/15774c77-7a81-11ed-897b-0e0713e7f869"
    
    @Column(name = "display_name")
    private String displayName; // e.g., "12 lead EKG Device Metric"
    
    @Column(name = "device_uuid", nullable = false, unique = true)
    private String deviceUuid;
    
    @Column(name = "account_number", nullable = false, length = 4)
    private String accountNumber;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
