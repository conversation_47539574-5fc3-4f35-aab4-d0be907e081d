package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "json_messages")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JsonMessage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "account_number", nullable = false, length = 10)
    private String accountNumber;
    
    @Column(name = "device_id", nullable = false)
    private String deviceId;
    
    @Column(name = "message_type", nullable = false)
    private String messageType; // REST or WEBSOCKET
    
    @Column(name = "endpoint", nullable = false)
    private String endpoint; // The endpoint that received the message
    
    @Column(name = "json_data", nullable = false, columnDefinition = "TEXT")
    private String jsonData; // Raw JSON data as received
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "client_ip")
    private String clientIp;
    
    @Column(name = "user_agent")
    private String userAgent;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
