package in.lakshay.medicalDummyServer.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "device_associations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceAssociation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "account_number", nullable = false, length = 4)
    private String accountNumber;
    
    @Column(name = "device_id", nullable = false, unique = true)
    private String deviceId;
    
    @Column(name = "auth_token", nullable = false, columnDefinition = "TEXT")
    private String authToken;

    @Column(name = "login_url", nullable = false)
    private String loginUrl = "http://localhost:8080";

    @Column(name = "base_uri", nullable = false)
    private String baseUri = "/oauth/token";
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
