package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.entity.JsonMessage;
import in.lakshay.medicalDummyServer.service.JsonMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private JsonMessageService jsonMessageService;
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "Medical Dummy Server",
            "version", "1.0.0"
        ));
    }
    
    @GetMapping("/messages")
    public ResponseEntity<List<JsonMessage>> getAllMessages() {
        return ResponseEntity.ok(jsonMessageService.getAllMessages());
    }

    @GetMapping("/messages/account/{accountNumber}")
    public ResponseEntity<List<JsonMessage>> getMessagesByAccount(@PathVariable String accountNumber) {
        return ResponseEntity.ok(jsonMessageService.getMessagesByAccount(accountNumber));
    }

    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        return ResponseEntity.ok(jsonMessageService.getStatistics());
    }
    
    @GetMapping("/sample-json")
    public ResponseEntity<Map<String, Object>> getSampleJson() {
        return ResponseEntity.ok(Map.of(
            "type", "medical_data",
            "id", "sample-" + System.currentTimeMillis(),
            "timestamp", java.time.LocalDateTime.now().toString(),
            "data", Map.of(
                "heartRate", 72,
                "bloodPressure", Map.of(
                    "systolic", 120,
                    "diastolic", 80
                ),
                "temperature", 98.6,
                "oxygenSaturation", 98
            ),
            "device", Map.of(
                "id", "device-001",
                "type", "monitor",
                "location", "room-101"
            ),
            "patient", Map.of(
                "id", "patient-123",
                "name", "John Doe"
            )
        ));
    }
}
