package in.lakshay.medicalDummyServer.controller;

import java.util.Base64;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import in.lakshay.medicalDummyServer.dto.JwtTokenResponse;
import in.lakshay.medicalDummyServer.service.JwtService;

@RestController
@RequestMapping("/oauth")
public class OAuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(OAuthController.class);
    
    @Autowired
    private JwtService jwtService;
    
    @RequestMapping(value = "/token", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseEntity<JwtTokenResponse> getToken(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam String accountNumber,
            @RequestParam String username,
            @RequestParam String Origin,
            @RequestParam String Token) {
        
        logger.info("JWT token request for account: {} and username: {}", accountNumber, username);
        logger.debug("Authorization header: {}", authHeader);

        try {
            // Validate Authorization header - accept both Basic and Bearer
            if (authHeader == null ||
                (!authHeader.startsWith("Basic ") && !authHeader.startsWith("basic ") &&
                 !authHeader.startsWith("Bearer ") && !authHeader.startsWith("bearer "))) {
                logger.warn("Invalid Authorization header format: {}", authHeader);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            // Extract credentials based on auth type
            String base64Credentials;
            if (authHeader.toLowerCase().startsWith("bearer ")) {
                // For Bearer tokens, use the token directly as base64 credentials
                base64Credentials = authHeader.substring(authHeader.indexOf(' ') + 1);
                logger.debug("Processing Bearer token authentication");
            } else {
                // For Basic auth, extract the base64 credentials
                base64Credentials = authHeader.substring(authHeader.indexOf(' ') + 1);
                logger.debug("Processing Basic authentication");
            }
            // For Bearer tokens, skip credential validation since it's just the token
            if (authHeader.toLowerCase().startsWith("bearer ")) {
                logger.debug("Bearer token received, skipping credential validation");
            } else {
                // For Basic auth, validate the credentials
                String credentials;
                try {
                    credentials = new String(Base64.getDecoder().decode(base64Credentials));
                } catch (IllegalArgumentException e) {
                    logger.warn("Invalid Base64 encoding in Authorization header");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
                }

                // For demo purposes, we'll accept any valid Basic auth format
                // In production, you would validate against actual credentials
                if (!credentials.contains(":")) {
                    logger.warn("Invalid Basic auth format");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
                }
                logger.debug("Basic auth credentials validated");
            }
            
            // Validate account number format
            if (!accountNumber.matches("\\d{4}")) {
                logger.warn("Invalid account number format: {}", accountNumber);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
            
            // Generate JWT tokens
            Long userId = 20950L; // Default user ID for demo
            String accessToken = jwtService.generateAccessToken(accountNumber, userId, username);
            String refreshToken = jwtService.generateRefreshToken(accountNumber, userId, username);
            long expiresIn = jwtService.getExpirationTime() / 1000; // Convert to seconds
            
            JwtTokenResponse response = new JwtTokenResponse(
                accountNumber, accessToken, refreshToken, expiresIn);
            
            logger.info("Successfully generated JWT tokens for account: {}", accountNumber);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error generating JWT token", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
