package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.dto.AuthorizationResponse;
import in.lakshay.medicalDummyServer.entity.DeviceAssociation;
import in.lakshay.medicalDummyServer.repository.DeviceAssociationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/api/v1")
public class AuthorizationController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthorizationController.class);
    
    @Autowired
    private DeviceAssociationRepository deviceAssociationRepository;
    
    private final String baseUrl = "http://localhost:8080";
    
    @GetMapping("/associate/{account}/{deviceId}")
    public ResponseEntity<AuthorizationResponse> associateDevice(
            @PathVariable String account,
            @PathVariable String deviceId) {
        
        logger.info("Authorization request for account: {} and deviceId: {}", account, deviceId);
        
        try {
            // Validate account number format (should be 4 digits)
            if (!account.matches("\\d{4}")) {
                logger.warn("Invalid account number format: {}", account);
                return ResponseEntity.ok(new AuthorizationResponse(
                    "Invalid account number format. Must be 4 digits."));
            }
            
            // Check if device is associated with the account
            Optional<DeviceAssociation> associationOpt = deviceAssociationRepository
                .findByAccountNumberAndDeviceIdAndIsActive(account, deviceId, true);
            
            if (associationOpt.isEmpty()) {
                logger.warn("Device {} not found for account {}", deviceId, account);
                return ResponseEntity.ok(new AuthorizationResponse(
                    String.format("Failed to associate device %s to organization %s", deviceId, account)));
            }
            
            DeviceAssociation association = associationOpt.get();
            
            // Return successful response
            AuthorizationResponse response = new AuthorizationResponse(
                association.getAuthToken(),
                association.getLoginUrl(),
                association.getBaseUri(),
                association.getAccountNumber()
            );
            
            logger.info("Successfully authorized device {} for account {}", deviceId, account);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error during device authorization", e);
            return ResponseEntity.ok(new AuthorizationResponse(
                "Internal server error during device authorization"));
        }
    }
}
