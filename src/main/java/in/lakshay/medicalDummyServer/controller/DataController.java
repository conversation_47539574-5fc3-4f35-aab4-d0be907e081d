package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.entity.JsonMessage;
import in.lakshay.medicalDummyServer.service.JsonMessageService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/data")
public class DataController {
    
    private static final Logger logger = LoggerFactory.getLogger(DataController.class);
    
    @Autowired
    private JsonMessageService jsonMessageService;
    
    // Accept any JSON data via POST
    @PostMapping("/{account}/{deviceId}")
    public ResponseEntity<Map<String, Object>> receiveData(
            @PathVariable String account,
            @PathVariable String deviceId,
            @RequestBody String jsonData,
            HttpServletRequest request) {
        
        logger.info("Received data for account: {} and device: {}", account, deviceId);
        
        try {
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            String endpoint = request.getRequestURI();
            
            JsonMessage saved = jsonMessageService.saveMessage(
                account, deviceId, "REST", endpoint, jsonData, clientIp, userAgent);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Data received and stored successfully",
                "messageId", saved.getId(),
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
            
        } catch (Exception e) {
            logger.error("Error processing data for account: {} and device: {}", account, deviceId, e);
            return ResponseEntity.status(500).body(Map.of(
                "status", "error",
                "message", "Failed to process data: " + e.getMessage(),
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
        }
    }
    
    // Get all messages
    @GetMapping("/messages")
    public ResponseEntity<List<JsonMessage>> getAllMessages() {
        return ResponseEntity.ok(jsonMessageService.getAllMessages());
    }
    
    // Get messages by account
    @GetMapping("/messages/account/{account}")
    public ResponseEntity<List<JsonMessage>> getMessagesByAccount(@PathVariable String account) {
        return ResponseEntity.ok(jsonMessageService.getMessagesByAccount(account));
    }
    
    // Get messages by account and device
    @GetMapping("/messages/account/{account}/device/{deviceId}")
    public ResponseEntity<List<JsonMessage>> getMessagesByAccountAndDevice(
            @PathVariable String account,
            @PathVariable String deviceId) {
        return ResponseEntity.ok(jsonMessageService.getMessagesByAccountAndDevice(account, deviceId));
    }
    
    // Get messages by type (REST or WEBSOCKET)
    @GetMapping("/messages/type/{messageType}")
    public ResponseEntity<List<JsonMessage>> getMessagesByType(@PathVariable String messageType) {
        return ResponseEntity.ok(jsonMessageService.getMessagesByType(messageType));
    }
    
    // Get messages by endpoint
    @GetMapping("/messages/endpoint")
    public ResponseEntity<List<JsonMessage>> getMessagesByEndpoint(@RequestParam String endpoint) {
        return ResponseEntity.ok(jsonMessageService.getMessagesByEndpoint(endpoint));
    }
    
    // Get statistics
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        return ResponseEntity.ok(jsonMessageService.getStatistics());
    }
    
    // Get account statistics
    @GetMapping("/statistics/account/{account}")
    public ResponseEntity<Map<String, Object>> getAccountStatistics(@PathVariable String account) {
        return ResponseEntity.ok(jsonMessageService.getAccountStatistics(account));
    }
    
    // Delete all messages
    @DeleteMapping("/messages")
    public ResponseEntity<Map<String, Object>> deleteAllMessages() {
        jsonMessageService.deleteAllMessages();
        return ResponseEntity.ok(Map.of(
            "status", "success",
            "message", "All messages deleted",
            "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        ));
    }
    
    // Delete messages by account
    @DeleteMapping("/messages/account/{account}")
    public ResponseEntity<Map<String, Object>> deleteMessagesByAccount(@PathVariable String account) {
        jsonMessageService.deleteMessagesByAccount(account);
        return ResponseEntity.ok(Map.of(
            "status", "success",
            "message", "Messages deleted for account: " + account,
            "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        ));
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
