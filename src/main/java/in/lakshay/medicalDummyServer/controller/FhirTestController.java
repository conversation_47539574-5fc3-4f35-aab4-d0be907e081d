package in.lakshay.medicalDummyServer.controller;

import in.lakshay.medicalDummyServer.websocket.FhirWebSocketHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/test/fhir")
public class FhirTestController {

    private static final Logger logger = LoggerFactory.getLogger(FhirTestController.class);

    @Autowired
    private FhirWebSocketHandler fhirWebSocketHandler;

    /**
     * Send a test Patient resource to a specific device
     */
    @PostMapping("/send-patient/{account}/{deviceId}")
    public ResponseEntity<Map<String, String>> sendTestPatient(
            @PathVariable String account,
            @PathVariable String deviceId) {
        
        try {
            String testPatientMessage = createTestPatientMessage(account, deviceId);
            fhirWebSocketHandler.sendMessageToSession(account, deviceId, testPatientMessage);
            
            logger.info("Sent test Patient resource to account: {} device: {}", account, deviceId);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Test Patient resource sent successfully",
                "account", account,
                "deviceId", deviceId,
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
            
        } catch (Exception e) {
            logger.error("Error sending test Patient resource", e);
            return ResponseEntity.ok(Map.of(
                "status", "error",
                "message", "Failed to send test Patient resource: " + e.getMessage(),
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
        }
    }

    /**
     * Send a test OperationOutcome to a specific device
     */
    @PostMapping("/send-operation-outcome/{account}/{deviceId}")
    public ResponseEntity<Map<String, String>> sendTestOperationOutcome(
            @PathVariable String account,
            @PathVariable String deviceId,
            @RequestParam(defaultValue = "information") String severity,
            @RequestParam(defaultValue = "Test operation outcome message") String message) {
        
        try {
            String testOperationOutcome = createTestOperationOutcome(severity, message);
            fhirWebSocketHandler.sendMessageToSession(account, deviceId, testOperationOutcome);
            
            logger.info("Sent test OperationOutcome to account: {} device: {}", account, deviceId);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Test OperationOutcome sent successfully",
                "account", account,
                "deviceId", deviceId,
                "severity", severity,
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
            
        } catch (Exception e) {
            logger.error("Error sending test OperationOutcome", e);
            return ResponseEntity.ok(Map.of(
                "status", "error",
                "message", "Failed to send test OperationOutcome: " + e.getMessage(),
                "timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            ));
        }
    }

    /**
     * Get sample FHIR Observation that the Android app might send
     */
    @GetMapping("/sample-observation")
    public ResponseEntity<String> getSampleObservation() {
        String sampleObservation = """
            {
                "resourceType": "Observation",
                "id": "sample-observation-123",
                "meta": {
                    "lastUpdated": "%s"
                },
                "status": "final",
                "category": [
                    {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                "code": "vital-signs",
                                "display": "Vital Signs"
                            }
                        ]
                    }
                ],
                "code": {
                    "coding": [
                        {
                            "system": "http://loinc.org",
                            "code": "8867-4",
                            "display": "Heart rate"
                        }
                    ]
                },
                "subject": {
                    "reference": "Patient/test-patient-123"
                },
                "effectiveDateTime": "%s",
                "valueQuantity": {
                    "value": 72,
                    "unit": "beats/minute",
                    "system": "http://unitsofmeasure.org",
                    "code": "/min"
                },
                "device": {
                    "reference": "Device/spacelabs-sibel-patch"
                }
            }
            """.formatted(
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            );
        
        return ResponseEntity.ok(sampleObservation);
    }

    private String createTestPatientMessage(String account, String deviceId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String patientId = "test-patient-" + deviceId;
        String patientIdentifier = account + "-" + deviceId;
        
        return String.format("""
            {
                "resourceType": "Patient",
                "id": "%s",
                "meta": {
                    "versionId": "1",
                    "lastUpdated": "%s"
                },
                "identifier": [
                    {
                        "use": "usual",
                        "system": "http://spacelabs.com/patient-id",
                        "value": "%s"
                    }
                ],
                "active": true,
                "name": [
                    {
                        "use": "official",
                        "family": "TestPatient",
                        "given": ["Manual", "Test"]
                    }
                ],
                "gender": "unknown",
                "birthDate": "1990-01-01"
            }
            """, patientId, timestamp, patientIdentifier);
    }

    private String createTestOperationOutcome(String severity, String message) {
        String outcomeId = "test-outcome-" + UUID.randomUUID().toString();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        return String.format("""
            {
                "resourceType": "OperationOutcome",
                "id": "%s",
                "meta": {
                    "lastUpdated": "%s"
                },
                "issue": [
                    {
                        "severity": "%s",
                        "code": "informational",
                        "details": {
                            "text": "%s"
                        }
                    }
                ]
            }
            """, outcomeId, timestamp, severity, message);
    }
}
