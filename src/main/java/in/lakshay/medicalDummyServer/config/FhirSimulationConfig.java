package in.lakshay.medicalDummyServer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "fhir.simulation")
public class FhirSimulationConfig {
    
    /**
     * Interval in seconds for sending Patient resource updates
     */
    private int patientUpdateIntervalSeconds = 30;
    
    /**
     * Initial delay in seconds before sending first Patient resource
     */
    private int initialPatientDelaySeconds = 2;
    
    /**
     * Whether to enable automatic Patient resource updates
     */
    private boolean enablePatientUpdates = true;
    
    /**
     * Whether to send detailed FHIR OperationOutcome responses
     */
    private boolean enableFhirResponses = true;
    
    // Getters and Setters
    
    public int getPatientUpdateIntervalSeconds() {
        return patientUpdateIntervalSeconds;
    }
    
    public void setPatientUpdateIntervalSeconds(int patientUpdateIntervalSeconds) {
        this.patientUpdateIntervalSeconds = patientUpdateIntervalSeconds;
    }
    
    public int getInitialPatientDelaySeconds() {
        return initialPatientDelaySeconds;
    }
    
    public void setInitialPatientDelaySeconds(int initialPatientDelaySeconds) {
        this.initialPatientDelaySeconds = initialPatientDelaySeconds;
    }
    
    public boolean isEnablePatientUpdates() {
        return enablePatientUpdates;
    }
    
    public void setEnablePatientUpdates(boolean enablePatientUpdates) {
        this.enablePatientUpdates = enablePatientUpdates;
    }
    
    public boolean isEnableFhirResponses() {
        return enableFhirResponses;
    }
    
    public void setEnableFhirResponses(boolean enableFhirResponses) {
        this.enableFhirResponses = enableFhirResponses;
    }
}
