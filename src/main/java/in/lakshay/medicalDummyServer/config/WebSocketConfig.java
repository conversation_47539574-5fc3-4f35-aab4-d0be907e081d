package in.lakshay.medicalDummyServer.config;

import in.lakshay.medicalDummyServer.websocket.FhirWebSocketHandler;
import in.lakshay.medicalDummyServer.websocket.JwtHandshakeInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private FhirWebSocketHandler fhirWebSocketHandler;
    
    @Autowired
    private JwtHandshakeInterceptor jwtHandshakeInterceptor;
    
    @Value("${websocket.allowed-origins}")
    private String allowedOrigins;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(fhirWebSocketHandler, "/api/v1/fhir/{account}/{deviceId}/store")
                .addInterceptors(jwtHandshakeInterceptor)
                .setAllowedOrigins(allowedOrigins);
    }
}
