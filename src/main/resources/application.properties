spring.application.name=medicalDummyServer

# Server Configuration
server.port=8080

# Database Configuration (PostgreSQL)
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# WebSocket Configuration
websocket.allowed-origins=*

# FHIR Simulation Configuration
fhir.simulation.patient-update-interval-seconds=30
fhir.simulation.initial-patient-delay-seconds=2
fhir.simulation.enable-patient-updates=true
fhir.simulation.enable-fhir-responses=true

# Logging Configuration
logging.level.in.lakshay.medicalDummyServer=INFO
logging.level.org.springframework.web.socket=INFO
