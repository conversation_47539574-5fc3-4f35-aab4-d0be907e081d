<!DOCTYPE html>
<html>
<head>
    <title>Medical Dummy Server WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #005a87; }
        .log { background: #f5f5f5; padding: 10px; height: 200px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .input-group { margin: 10px 0; }
        .input-group label { display: inline-block; width: 120px; }
        .input-group input { width: 200px; padding: 5px; }
        textarea { width: 100%; height: 150px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Medical Dummy Server - JSON Data Test</h1>
        
        <div class="section">
            <h3>1. Get JWT Token</h3>
            <div class="input-group">
                <label>Account:</label>
                <input type="text" id="account" value="1234">
            </div>
            <div class="input-group">
                <label>Username:</label>
                <input type="text" id="username" value="hubapi">
            </div>
            <button class="button" onclick="getJwtToken()">Get JWT Token</button>
            <div>JWT Token: <span id="jwtToken" style="font-family: monospace; font-size: 12px;"></span></div>
        </div>

        <div class="section">
            <h3>2. WebSocket Connection</h3>
            <div class="input-group">
                <label>Device ID:</label>
                <input type="text" id="deviceId" value="************">
            </div>
            <button class="button" onclick="connectWebSocket()">Connect WebSocket</button>
            <button class="button" onclick="disconnectWebSocket()">Disconnect</button>
            <div>Status: <span id="wsStatus">Disconnected</span></div>
        </div>

        <div class="section">
            <h3>3. Send JSON Messages</h3>
            <button class="button" onclick="sendSampleVitals()">Send Sample Vitals</button>
            <button class="button" onclick="sendSampleAlert()">Send Sample Alert</button>
            <br><br>
            <label>Custom Message:</label>
            <textarea id="customMessage" placeholder="Enter any JSON message here..."></textarea>
            <br>
            <button class="button" onclick="sendCustomMessage()">Send Custom Message</button>
        </div>

        <div class="section">
            <h3>4. Message Log</h3>
            <button class="button" onclick="clearLog()">Clear Log</button>
            <div id="messageLog" class="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let jwtToken = '';

        function log(message) {
            const logDiv = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        async function getJwtToken() {
            const account = document.getElementById('account').value;
            const username = document.getElementById('username').value;
            
            try {
                const response = await fetch(`http://localhost:8080/oauth/token?accountNumber=${account}&username=${username}&Origin=EMR&Token=sample`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Basic ********************************************'
                    }
                });
                
                const data = await response.json();
                jwtToken = data.access_token;
                document.getElementById('jwtToken').textContent = jwtToken.substring(0, 50) + '...';
                log('JWT Token obtained successfully');
            } catch (error) {
                log('Error getting JWT token: ' + error.message);
            }
        }

        function connectWebSocket() {
            if (!jwtToken) {
                log('Please get JWT token first');
                return;
            }

            const account = document.getElementById('account').value;
            const deviceId = document.getElementById('deviceId').value;

            // For browser testing, we pass the JWT token as a query parameter
            // since browser WebSocket API doesn't support custom headers
            const wsUrl = `ws://localhost:8080/api/v1/fhir/${account}/${deviceId}/store?token=${jwtToken}`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                document.getElementById('wsStatus').textContent = 'Connected';
                log('WebSocket connected');
                log('Note: JWT token passed as query parameter for browser compatibility');
            };
            
            ws.onmessage = function(event) {
                log('Received: ' + event.data);
            };
            
            ws.onclose = function(event) {
                document.getElementById('wsStatus').textContent = 'Disconnected';
                log('WebSocket disconnected');
            };
            
            ws.onerror = function(error) {
                log('WebSocket error: ' + error);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendSampleVitals() {
            const message = {
                "type": "vitals",
                "id": "vitals-" + Date.now(),
                "timestamp": new Date().toISOString(),
                "patient": {
                    "id": "patient-123",
                    "name": "John Doe"
                },
                "device": {
                    "id": "device-001",
                    "type": "monitor"
                },
                "data": {
                    "heartRate": Math.floor(Math.random() * 40) + 60,
                    "bloodPressure": {
                        "systolic": Math.floor(Math.random() * 40) + 110,
                        "diastolic": Math.floor(Math.random() * 20) + 70
                    },
                    "temperature": (Math.random() * 2 + 97).toFixed(1),
                    "oxygenSaturation": Math.floor(Math.random() * 5) + 95
                }
            };

            sendMessage(message);
        }

        function sendSampleAlert() {
            const message = {
                "type": "alert",
                "id": "alert-" + Date.now(),
                "timestamp": new Date().toISOString(),
                "severity": "HIGH",
                "patient": {
                    "id": "patient-123",
                    "name": "John Doe"
                },
                "device": {
                    "id": "device-001",
                    "type": "monitor"
                },
                "alert": {
                    "code": "HR_HIGH",
                    "message": "Heart rate above threshold",
                    "value": 120,
                    "threshold": 100
                }
            };

            sendMessage(message);
        }

        function sendCustomMessage() {
            const customText = document.getElementById('customMessage').value;
            if (!customText.trim()) {
                log('Please enter a custom message');
                return;
            }
            
            try {
                const message = JSON.parse(customText);
                sendMessage(message);
            } catch (error) {
                log('Invalid JSON: ' + error.message);
            }
        }

        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket not connected');
                return;
            }
            
            const messageStr = JSON.stringify(message, null, 2);
            ws.send(messageStr);
            log('Sent: ' + messageStr);
        }
    </script>
</body>
</html>
